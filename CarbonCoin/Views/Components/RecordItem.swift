//
//  RecordItem.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/3.
//

import SwiftUI

// MARK: - 日志记录项视图

/// 日志记录项视图，支持精简版和详细版两种显示模式
struct RecordItem: View {

    // MARK: - Properties

    /// 日志数据
    let log: UserLog

    /// 显示模式
    let displayMode: DisplayMode

    /// 点击回调
    let onTap: (() -> Void)?

    // MARK: - Display Mode

    enum DisplayMode {
        case compact    // 精简版
        case detailed   // 详细版（暂未实现）
    }

    // MARK: - Initialization

    init(log: UserLog,
         displayMode: DisplayMode = .compact,
         onTap: (() -> Void)? = nil) {
        self.log = log
        self.displayMode = displayMode
        self.onTap = onTap
    }

    // MARK: - Body

    var body: some View {
        switch displayMode {
        case .compact:
            compactView
        case .detailed:
            detailedView // 暂未实现，返回精简版
        }
    }

    // MARK: - Compact View

    private var compactView: some View {
        Button(action: {
            onTap?()
        }) {
            HStack(spacing: Theme.Spacing.md) {
                // 左侧图标
                iconView
                
                Spacer()

                // 右侧内容
                VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
                    // 标题和描述
                    titleAndDescriptionView

                    // 位置信息
                    locationView

                    // 时间和统计信息
                    timeAndStatsView
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(Theme.Spacing.md)
                .background(
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                        .fill(Color.cardBackground)
                        .overlay(
                            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                                .stroke(Color.gray.opacity(0.5), lineWidth: 1)
                        )
                )
                .cardShadow()
            }
            .padding(.trailing, Theme.Spacing.md)
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Detailed View (暂未实现)

    private var detailedView: some View {
        compactView // 暂时返回精简版
    }

    // MARK: - Icon View

    private var iconView: some View {
        ZStack {
            // 背景圆圈
            Circle()
                .fill(iconBackgroundColor)
                .frame(width: 48, height: 48)

            // 图标
            Image(iconName)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 24, height: 24)
        }
    }

    // MARK: - Title and Description View

    private var titleAndDescriptionView: some View {
        VStack(alignment: .leading, spacing: 2) {
            // 主标题
//            Text(titleText)
//                .font(.title3Brand)
//                .foregroundColor(.textPrimary)
//                .lineLimit(1)

            // 描述文本
            if let description = log.description, !description.isEmpty {
                Text(description)
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)
                    .lineLimit(2)
            }
        }
    }

    // MARK: - Location View

    private var locationView: some View {
        Group {
            if let locationText = locationText {
                Text(locationText)
                    .font(.captionBrand)
                    .foregroundColor(.textTertiary)
                    .lineLimit(1)
            }
        }
    }

    // MARK: - Time and Stats View

    private var timeAndStatsView: some View {
        HStack(spacing: Theme.Spacing.sm) {
            // 时间
            Text(timeText)
                .font(.captionBrand)
                .foregroundColor(.textTertiary)

            Spacer()

            // 统计信息（点赞、评论等）
            statsView
        }
    }

    // MARK: - Stats View

    private var statsView: some View {
        HStack(spacing: Theme.Spacing.sm) {
            // 点赞数
            if let likes = log.likes, !likes.isEmpty {
                HStack(spacing: 2) {
                    Image(systemName: "heart.fill")
                        .font(.caption)
                        .foregroundColor(.orange)
                    Text("\(likes.count)")
                        .font(.captionBrand)
                        .foregroundColor(.textTertiary)
                }
            }

            // 评论数
            if let comments = log.comments, !comments.isEmpty {
                HStack(spacing: 2) {
                    Image(systemName: "bubble.fill")
                        .font(.caption)
                        .foregroundColor(.green)
                    Text("\(comments.count)")
                        .font(.captionBrand)
                        .foregroundColor(.textTertiary)
                }
            }

            // 距离信息（仅足迹记录）
            if log.recordType == .trip, let footprint = log.userFootprints {
                HStack(spacing: 2) {
                    Image(systemName: "location.fill")
                        .font(.caption)
                        .foregroundColor(.blue)
                    Text(String(format: "%.1f", footprint.totalDistance))
                        .font(.captionBrand)
                        .foregroundColor(.textTertiary)
                }
            }
        }
    }

    // MARK: - Computed Properties

    /// 图标名称
    private var iconName: String {
        // 如果是公开的，统一使用 log-public 图标
        if log.isPublic {
            return "log-public"
        }

        // 根据记录类型选择图标
        switch log.recordType {
        case .location:
            return "log-checkin"
        case .recognition:
            return "log-shopping"
        case .trip:
            // 足迹记录需要根据活动类型选择图标
            if let footprint = log.userFootprints {
                switch footprint.activityType.lowercased() {
                case "walking":
                    return "log-walk"
                case "cycling":
                    return "log-cycling"
                case "bus":
                    return "log-bus"
                case "subway":
                    return "log-subway"
                default:
                    return "log-walk" // 默认使用步行图标
                }
            }
            return "log-walk"
        }
    }

    /// 图标背景颜色
    // TODO: 使用pdf直接替换icon / 将渐变色与color一起处理
    private var iconBackgroundColor: Color {
        return Color.accent
    }

    /// 标题文本
    private var titleText: String {
        switch log.recordType {
        case .location:
            if let checkin = log.locationCheckIns {
                return checkin.position ?? "地点打卡"
            }
            return "地点打卡"
        case .recognition:
            if let card = log.itemCard {
                return card.title
            }
            return "卡片识别"
        case .trip:
            if let footprint = log.userFootprints {
                let activityName = getActivityDisplayName(footprint.activityType)
                return "\(activityName)出行"
            }
            return "出行记录"
        }
    }

    /// 位置文本
    private var locationText: String? {
        switch log.recordType {
        case .location:
            if let checkin = log.locationCheckIns {
                return checkin.position
            }
            return nil
        case .recognition:
            if let card = log.itemCard {
                return card.location
            }
            return nil
        case .trip:
            return "足迹记录" // 足迹记录显示固定文本
        }
    }

    /// 时间文本
    private var timeText: String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")

        let calendar = Calendar.current
        if calendar.isDateInToday(log.createdAt) {
            formatter.dateFormat = "今天 HH:mm"
        } else if calendar.isDateInYesterday(log.createdAt) {
            formatter.dateFormat = "昨天 HH:mm"
        } else {
            formatter.dateFormat = "MM月dd日 HH:mm"
        }

        return formatter.string(from: log.createdAt)
    }

    /// 获取活动类型显示名称
    private func getActivityDisplayName(_ activityType: String) -> String {
        switch activityType.lowercased() {
        case "walking":
            return "步行"
        case "cycling":
            return "骑行"
        case "bus":
            return "公交"
        case "subway":
            return "地铁"
        default:
            return "出行"
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: Theme.Spacing.md) {
        // 地点打卡示例
        RecordItem(
            log: UserLog(
                id: "1",
                userId: "user1",
                recordType: .location,
                recordId: "checkin1",
                imageList: nil,
                description: "和其余2项茶饮类消费",
                isPublic: false,
                createdAt: Date(),
                updatedAt: Date(),
                user: UserInfo(userId: "user1", nickname: "测试用户", avatarURL: nil),
                likes: Array(repeating: LogLike(id: "like1", userId: "user2", createdAt: Date(), user: UserInfo(userId: "user2", nickname: "用户2", avatarURL: nil)), count: 30),
                comments: Array(repeating: LogComment(id: "comment1", userId: "user3", content: "不错", replyTo: nil, createdAt: Date(), user: UserInfo(userId: "user3", nickname: "用户3", avatarURL: nil)), count: 10),
                locationCheckIns: LocationCheckIn(id: "checkin1", position: "余杭区城市街道", latitude: 30.0, longitude: 120.0, createdAt: Date()),
                userFootprints: nil,
                itemCard: nil
            )
        )

        // 公开足迹记录示例
        RecordItem(
            log: UserLog(
                id: "2",
                userId: "user1",
                recordType: .trip,
                recordId: "trip1",
                imageList: nil,
                description: "和其余2项茶饮类消费",
                isPublic: true,
                createdAt: Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date(),
                updatedAt: Date(),
                user: UserInfo(userId: "user1", nickname: "测试用户", avatarURL: nil),
                likes: Array(repeating: LogLike(id: "like2", userId: "user2", createdAt: Date(), user: UserInfo(userId: "user2", nickname: "用户2", avatarURL: nil)), count: 30),
                comments: Array(repeating: LogComment(id: "comment2", userId: "user3", content: "不错", replyTo: nil, createdAt: Date(), user: UserInfo(userId: "user3", nickname: "用户3", avatarURL: nil)), count: 10),
                locationCheckIns: nil,
                userFootprints: UserFootprint(id: "trip1", activityType: "cycling", isFinished: true, totalDistance: 0.8, createdAt: Date()),
                itemCard: nil
            )
        )
    }
    .padding()
    .globalBackground()
}
